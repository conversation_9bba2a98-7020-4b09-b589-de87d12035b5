import React, { useState, useEffect } from "react";

import type { Dispatch, SetStateAction } from "react";
import CountryList from "./CountryList";
import SellerInformation from "./SellerInformation";
import BlockSellerList from "./BlockSellerList";
import BlockItemsList from "./BlockItemsList";
import UserAuth from "./UserAuth";
import { useUserSettings } from "../../helper/UserSettingsContext";
import supabase from "../../helper/supabaseClient";
import { get_browser_storage, set_browser_storage } from "../browser_storage";
import config_json from "../../config.json";
import { ONE_DAY_IN_MS, type UserSettings } from "../files/types";
import { console_log, wait } from "../../helper/helpers";
import SessionManager from "../../helper/SessionManager";
//
interface TabContainerProps {}

const TabContainer: React.FC<TabContainerProps> = ({}) => {
  const [activeTab, setActiveTab] = useState("countries");
  const [extensionVersion, setExtensionVersion] = useState<string | null>(null);

  const [sellerActiveTab, setSellerActiveTab] = useState("sellerInfo");
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const { userSettings, update_user_settings, loading_status, set_loading_status, session, setSession } =
    useUserSettings();
  const [showUserAuth, setShowUserAuth] = useState(false);

  useEffect(() => {
    if (chrome.runtime && chrome.runtime.getManifest) {
      const manifest = chrome.runtime.getManifest();
      setExtensionVersion(manifest.version);
    }
  }, []);

  const handleToggle = async () => {
    const newToggleState = !userSettings.toggleState;
    userSettings.toggleState = newToggleState;
    update_user_settings({ ...userSettings, toggleState: newToggleState });
  };

  const handleSignOut = async (e: React.FormEvent<HTMLFormElement>) => {
    setShowUserAuth(false);
    e.preventDefault();

    // Use SessionManager to clear session
    const sessionManager = SessionManager.getInstance();
    await sessionManager.clearSession();

    const { error } = await supabase.auth.signOut();
    if (error) {
      console_log("Error", error.message);
      return;
    }

    let storage = await get_browser_storage(["browser_user_settings"]);
    // todo: calling this function triggers alert message inside of it
    // that is why we won't see the "Logge out." message triggered in this function
    update_user_settings(storage.browser_user_settings as UserSettings);
    //
    set_loading_status({
      status: "active",
      type: "alert-success",
      message: "Logged out.",
    });
    await wait(1500);
    set_loading_status({
      status: "not_active",
      type: "alert-success",
      message: "Logged out.",
    });
    //
    setSession(null);
    setIsLoggedIn(true);
  };

  const handleSyncData = (e: any, close: boolean) => {
    e.preventDefault();

    if (close) {
      setShowUserAuth(false);
    } else {
      setShowUserAuth(true);
    }
  };

  const testingSetBackActivationTimestmp = () => {
    let two_days_ago = Date.now() - 2 * ONE_DAY_IN_MS;
    update_user_settings({ ...userSettings, lastClickTime: two_days_ago });
  };

  const openPopupInNewTab = () => {
    chrome.tabs.create({
      active: true,
      url: chrome.runtime.getURL(`action/default_popup.html`),
    });
  };

  const TabInfo = (
    <div className="w-full pr-2">
      <SellerInformation />
    </div>
  );

  const BlockSellerTab = (
    <div className="w-full pr-2">
      <BlockSellerList toggleState={userSettings.toggleState} />
    </div>
  );

  const BlockItemsTab = (
    <div className="w-full pr-2">
      <BlockItemsList toggleState={userSettings.toggleState} />
    </div>
  );

  return (
    <section className="container box-border mx-auto p-5 inset-0 relative">
      {/* Main Tabs for Countries and Sellers */}
      {/* Sign Out Button - Only Shows Above Seller Tabs */}
      <div className="flex justify-between items-center pb-4">
        <p className="text-2xl font-sm">uBuyAssist v{extensionVersion}</p>
        <div className="flex gap-2 items-center">
          <p className="mt-2 text-white text-xsm">All features {userSettings.toggleState ? "Enabled" : "Disabled"}</p>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={userSettings.toggleState}
              onChange={handleToggle}
            />
            <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:bg-blue-600 transition-colors"></div>
            <div className="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform peer-checked:translate-x-5 transition-transform"></div>
          </label>
        </div>
      </div>
      {session ? (
        <div className="bg-white/10 flex justify-between p-1 rounded-lg mb-2 items-center">
          <p className="text-2xl">Logged in</p>
          <button className="btn btn-primary" onClick={(e) => handleSignOut(e as any)}>
            Sign out
          </button>
        </div>
      ) : (
        <div className="bg-white/10 ">
          <div className="flex justify-between p-4 rounded-lg mb-2 items-center">
            <button className="btn btn-primary" onClick={(e) => handleSyncData(e, false)}>
              Sign in
            </button>
            {showUserAuth && (
              <button className="btn btn-secondary" onClick={(e) => handleSyncData(e, true)}>
                Close
              </button>
            )}
          </div>
          {showUserAuth && <UserAuth signup={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />}
        </div>
      )}
      <div className="tabs tabs-boxed mb-6">
        <button
          className={`tab ${activeTab === "countries" ? "tab-active" : ""}`}
          onClick={() => setActiveTab("countries")}
        >
          Countries
        </button>
        <button className={`tab ${activeTab === "info" ? "tab-active" : ""}`} onClick={() => setActiveTab("info")}>
          Sellers
        </button>
        {config_json.enable_testing_tab && (
          <button
            className={`tab ${activeTab === "testing" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("testing")}
          >
            Testing
          </button>
        )}
      </div>

      {/* Nested Tabs for Seller Information */}
      {activeTab === "info" && (
        <div className="tabs tabs-boxed mb-6">
          <button
            className={`tab ${sellerActiveTab === "sellerInfo" ? "tab-active" : ""}`}
            onClick={() => setSellerActiveTab("sellerInfo")}
          >
            Seller Information
          </button>
          <button
            className={`tab ${sellerActiveTab === "blockSeller" ? "tab-active" : ""}`}
            onClick={() => setSellerActiveTab("blockSeller")}
          >
            Block Seller
          </button>
          <button
            className={`tab ${sellerActiveTab === "blockItems" ? "tab-active" : ""}`}
            onClick={() => setSellerActiveTab("blockItems")}
          >
            Block Items
          </button>
        </div>
      )}

      {activeTab === "testing" && (
        <div className="flex flex-col">
          <div className="mb-3">Tab for testing ( this will be removed in a production build)</div>
          <button className="btn btn-primary mb-2" onClick={(e) => testingSetBackActivationTimestmp()}>
            Set activation timestamp to "2 days ago"
          </button>{" "}
          <button className="btn btn-primary" onClick={(e) => openPopupInNewTab()}>
            Open popup in new tab
          </button>
        </div>
      )}

      <div className="w-full h-full flex justify-between">
        {activeTab === "countries" && <CountryList />}
        {activeTab === "info" && sellerActiveTab === "sellerInfo" && TabInfo}
        {activeTab === "info" && sellerActiveTab === "blockSeller" && BlockSellerTab}
        {activeTab === "info" && sellerActiveTab === "blockItems" && BlockItemsTab}
      </div>

      {/* Toast Notification */}
      {/* todo: add a progress indicator that also acts as a blocking overlay */}
      {loading_status.status === "active" && (
        <div className={`toast toast-end`}>
          <div className={`alert ${loading_status.type}`}>
            <span>{loading_status.message}</span>
          </div>
        </div>
      )}
    </section>
  );
};

export default TabContainer;
